# SPDX-License-Identifier: GPL-2.0
dtb-$(CONFIG_ARCH_ROCKCHIP) += px30-evb.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += px30-engicam-px30-core-ctouch2.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += px30-engicam-px30-core-ctouch2-of10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += px30-engicam-px30-core-edimm2.2.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += px30-evb-ddr3-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += px30-evb-ddr3-v10-avb.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += px30-evb-ddr3-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += px30-mini-evb-ddr3-v11.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += px30-mini-evb-ddr3-v11-avb.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += px30-evb-ddr3-v11-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += px30-evb-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += px30-evb-ddr4-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308-evb.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308-evb-amic-v11.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308-evb-amic-v13.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308-evb-audio-amic-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308-evb-audio-amic-v11.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308-evb-audio-v10-amp-display-rgb.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308-evb-audio-v11-amp-display-rgb.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308-evb-audio-v10-display-rgb.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308-evb-audio-v11-display-rgb.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308-evb-audio-v10-partybox.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308-evb-audio-v11-partybox.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308-evb-dmic-pdm-v11.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308-evb-dmic-pdm-v13.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308-partybox-coreboard-demo-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308-partybox-ext-rolling-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308-roc-cc.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308-rock-pi-s.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308b-evb-amic-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308b-evb-amic-v10-amp.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308bs-evb-amic-v11.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308bs-evb-ddr3-v20-rk618-rgb2dsi.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308bs-evb-dmic-pdm-v11.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308bs-evb-mcu-display-v20.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308bs-evb-mipi-display-v11.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3308bs-evb-rgb-display-v20.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3318-a95x-z2.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3326-evb-lp3-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3326-evb-lp3-v10-avb.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3326-evb-lp3-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3326-evb-lp3-v11.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3326-evb-lp3-v11-avb.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3326-evb-lp3-v12-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3326-odroid-go2.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3326-863-lp3-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3326-863-lp3-v10-avb.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3326-863-lp3-v10-rkisp1.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3328-a1.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3328-evb.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3328-nanopi-r2s.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3328-rock64.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3328-rock-pi-e.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3328-roc-cc.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3328-roc-pc.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3358-evb-ddr3-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3358m-automotive-ddr3-v11-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3358m-automotive-ddr3-v11-linux-tb.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3358m-vehicle-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3368-evb-act8846.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3368-geekbox.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3368-lion-haikou.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3368-orion-r68-meta.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3368-px5-evb.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3368-r88.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-eaidk-610.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-evb.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-ficus.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-firefly.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-gru-bob.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-gru-kevin.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-gru-scarlet-dumo.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-gru-scarlet-inx.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-gru-scarlet-kd.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-hugsun-x99.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-khadas-edge.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-khadas-edge-captain.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-khadas-edge-v.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-kobol-helios64.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-leez-p710.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-nanopc-t4.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-nanopi-m4.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-nanopi-m4b.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-nanopi-neo4.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-nanopi-r4s.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-nanopi-r4s-enterprise.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-orangepi.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-pinebook-pro.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-pinephone-pro.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-puma-haikou.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-roc-pc.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-roc-pc-mezzanine.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-roc-pc-plus.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-rock-4c-plus.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-rock-pi-4a.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-rock-pi-4a-plus.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-rock-pi-4b.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-rock-pi-4b-plus.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-rock-pi-4c.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-rock960.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-rockpro64-v2.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-rockpro64.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-sapphire.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-sapphire-excavator.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399-sapphire-excavator-edp-avb.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3399pro-rock-pi-n10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3518-evb1-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3528-demo1-lp4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3528-demo4-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3528-demo4-ddr4-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3528-demo6-ddr3-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3528-evb1-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3528-evb1-ddr4-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3528-evb1-ddr4-v10-spi-nand-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3528-evb2-ddr3-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3528-evb3-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3528-evb4-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3528-iotest-lp3-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-dictpen-test3-v20.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-evb1-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-evb1-lp4x-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-evb1-lp4x-v10-linux-amp.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-evb1-lp4x-v10-lvds.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-evb1-lp4x-v10-mcu-k350c4516t.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-evb1-lp4x-v10-rgb2lvds.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-evb1-lp4x-v10-rgb-FX070-DHM11BOE-A.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-evb1-lp4x-v10-rgb-k350c4516t.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-evb1-lp4x-v10-rgb-Q7050ITH2641AA1T.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-evb1-lp4x-v10-sii9022-bt1120-to-hdmi.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-evb1-lp4x-v10-sii9022-rgb2hdmi.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-evb1-lp4x-v10-spdif.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-evb2-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-evb2-ddr4-v10-dual-camera.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-evb2-ddr4-v10-image-reverse.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-evb2-ddr4-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-evb2-ddr4-v10-linux-amp.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-evb2-ddr4-v10-pdm-mic-array.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-evb2-ddr4-v10-sii9022-bt1120-to-hdmi.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-iotest-lp3-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-iotest-lp3-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-iotest-lp3-v10-dsm.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-project-demo-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-rk817-tablet-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-test1-ddr3-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-test2-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-toybrick-android.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562-toybrick-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3562j-core-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-box-demo-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-evb-mipitest-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-evb1-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-evb1-ddr4-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-evb1-ddr4-v10-lvds.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-evb2-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-evb2-lp4x-v10-edp.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-evb2-lp4x-v10-eink.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-evb2-lp4x-v10-i2s-mic-array.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-evb2-lp4x-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-evb2-lp4x-v10-pdm-mic-array.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-evb3-ddr3-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-evb3-ddr3-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-evb5-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-rk817-eink.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-rk817-eink-w6.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-rk817-eink-w103.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-rk817-tablet.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-rk817-tablet-k108.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-rk817-tablet-rkg11.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3566-rk817-tablet-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3567-evb2-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3567-evb2-lp4x-v10-dual-channel-lvds.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3567-evb2-lp4x-v10-one-vp-two-single-channel-lvds.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3567-evb2-lp4x-v10-single-channel-lvds.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3567-evb2-lp4x-v10-two-vp-two-separate-single-channel-lvds.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb-rk628-hdmi2bt1120-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb-rk628-hdmi2dsi-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb-rk628-hdmi2dsi-dual-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb-rk628-hdmi2gvi-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb-rk628-hdmi2lvds-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb-rk628-hdmi2lvds-dual-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb-rk628-rgb2dsi-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb-rk628-rgb2gvi-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb-rk628-rgb2hdmi-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb-rk628-rgb2lvds-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb-rk628-rgb2lvds-dual-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb1-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb1-ddr4-v10-dual-camera.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb1-ddr4-v10-edp-panel.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb1-ddr4-v10-one-vp-two-single-channel-lvds.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb1-ddr4-v10-one-vp-two-single-channel-lvds-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb1-ddr4-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb1-ddr4-v10-linux-amp.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb1-ddr4-v10-linux-spi-nor.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb1-ddr4-v10-single-channel-lvds.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb1-ddr4-v10-two-vp-two-separate-single-channel-lvds.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb2-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb2-lp4x-v10-bt1120-to-hdmi.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb4-lp3-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb5-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb6-ddr3-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb6-ddr3-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb6-ddr3-v10-rk630-bt656-to-cvbs.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb7-ddr4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb8-lp4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-evb8-lp4-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-iotest-ddr3-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-iotest-ddr3-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-nvr-demo-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-nvr-demo-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-nvr-demo-v10-linux-spi-nand.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-nvr-demo-v12-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-nvr-demo-v12-linux-spi-nand.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-pcie-ep-lp4x-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-toybrick-sd0-android.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-toybrick-sd0-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-toybrick-x0-android.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568-toybrick-x0-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-evb-camera-csi-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-evb-camera-dvp-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-evb-display-dsi0-command2dsi-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-evb-display-dsi0-command2lvds0-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-evb-display-dsi0-command2rgb-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-evb-display-dsi1-command2dsi-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-evb-display-dsi1-command2lvds0-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-evb-display-dsi1-command2rgb-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-evb-display-rgb2dsi-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-evb-display-rgb2lvds-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-evb-display-rgb2rgb-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-evb-display-lvds2lvds-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-evb-display-lvds2rgb-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-v1-evb-display-dsi0-command2dsi-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-v1-evb-display-dsi0-command2dual_lvds-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-v1-evb-display-dsi0-command2lvds0-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-v1-evb-display-dsi0-dsi1-command2dual_lvdsx2-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-v1-evb-display-dsi1-command2dsi-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-v1-evb-display-dsi1-command2dual_lvds-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-v1-evb-display-dsi1-command2lvds0-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-v1-evb-display-lvds2dsi-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-v1-evb-display-lvds2dual-lvds-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-v1-evb-display-lvds2dual-lvds-vehicle-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-v1-evb-display-lvds2lvds-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-v1-evb-display-lvds2rgb-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-v1-evb-display-rgb2dsi-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-v1-evb-display-rgb2dual-lvds-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-v1-evb-display-rgb2dual-lvds-vehicle-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-v1-evb-display-rgb2lvds-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-v1-evb-display-rgb2rgb-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-v1-evb-display-super-frame-dsi0-command2dsi-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3568m-serdes-v1-evb-display-super-frame-dsi0-command2lvds0-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-ebook-color-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-ebook-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-ebook-x3-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-evb1-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-evb1-v10-android9.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-evb1-v10-dv.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-evb1-v10-edp-NE160QAM-NX1.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-evb1-v10-edp-NV140QUM-N61.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-evb1-v10-hdmi2dp.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-evb1-v10-image-reverse-demo.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-evb1-v10-ipc-4x-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-evb1-v10-ipc-dual-cam-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-evb1-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-evb1-v10-linux-amp.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-evb1-v10-lontium-hdmiin.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-evb1-v10-nvp6324-ahd2csi.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-evb1-v10-pdm.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-evb1-v10-projector.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-evb1-v10-rk621-hdmi2csi.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-evb1-v10-rk628-hdmi2csi.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-evb1-v10-tp2815-ahd2csi.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-evb2-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-evb2-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-industry-evb-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-iotest-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-iotest-v10-edp2dp.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-iotest-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-nvr-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-tablet-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-tablet-v11.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-test1-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-test1-v10-eink.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-test1-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-test1-v10-mcu-k350c4516t.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-test1-v10-rgb-Q7050ITH2641AA1T.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-test1-v10-rk628-bt1120-2-hdmi.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-test1-v10-rk628-hdmi2gvi.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-test1-v10-sii9022-bt1120-to-hdmi.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-test1-v10-sii9022-rgb2hdmi.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-test1-v10-vopl-mipi-display-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-test2-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-test2-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-test3-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-test5-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-vehicle-evb-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-vehicle-evb-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-vehicle-evb-v20.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-vehicle-evb-v20-amp.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576-vehicle-evb-v20-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576s-evb1-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576s-evb1-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3576s-tablet-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb1-lp4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb1-lp4-v10-dsi-dsc-MV2100UZ1.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb1-lp4-v10-edp-8lanes-M280DCA.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb1-lp4-v10-edp-NE160QAM-NX1.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb1-lp4-v10-edp-NV140QUM-N61.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb1-lp4-v10-hdmi2dp.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb1-lp4-v10-ipc-6x-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb1-lp4-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb1-lp4-v10-linux-amp.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb1-lp4-v10-linux-ipc.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb1-lp4-v10-lt6911uxe.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb1-lp4-v10-lt6911uxc-dual-mipi.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb1-lp4-v10-rk628-hdmi2csi.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb2-lp4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb2-lp4-v10-edp.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb2-lp4-v10-edp2dp.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb2-lp4-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb3-lp5-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb3-lp5-v10-edp.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb3-lp5-v10-edp-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb3-lp5-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb4-lp4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb4-lp4-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb5-lp4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb5-lp4-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb6-lp4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb6-lp4-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb7-lp4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb7-lp4-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb7-lp4-v10-rk1608-ipc-8x-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb7-lp4-v11-linux-ipc.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb7-v11.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb7-v11-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-evb7-v11-rk628-hdmi2csi.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-nvr-demo-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-nvr-demo-v10-android.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-nvr-demo-v10-ipc-4x-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-nvr-demo-v10-spi-nand.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-nvr-demo1-v21.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-nvr-demo1-v21-android.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-nvr-demo3-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-nvr-demo3-v10-android.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-pcie-ep-demo-v11.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-pcie-ep-demo-v11-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-toybrick-x0-android.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-toybrick-x0-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-vehicle-evb-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-vehicle-evb-v20.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-vehicle-evb-v21.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-vehicle-evb-v22.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-vehicle-evb-v23.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588-vehicle-s66-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588s-evb1-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588s-evb1-lp4x-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588s-evb2-lp5-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588s-evb2-lp5-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588s-evb3-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588s-evb3-lp4x-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588s-evb3-lp4x-v10-nvp6158-ahd-to-bt1120.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588s-evb3-lp4x-v10-rk630-bt656-to-cvbs.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588s-evb3-lp4x-v10-sii9022-bt1120-to-hdmi.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588s-evb4-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588s-evb4-lp4x-v10-linux.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588s-evb8-lp4x-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588s-tablet-rk806-single-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588s-tablet-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rk3588s-tablet-v11.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rv1126b-evb1-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rv1126b-evb1-v10-bt-sco.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rv1126b-evb1-v10-dual-4k.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rv1126b-evb1-v10-dv.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rv1126b-evb1-v10-spi-nor.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rv1126b-evb2-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rv1126b-evb2-v10-mcu-k350c4516t.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rv1126b-evb2-v10-rgb-Q7050ITH2641AA1T.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rv1126b-evb2-v10-sii9022-bt1120-to-hdmi.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rv1126b-evb3-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rv1126b-evb4-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rv1126b-iotest-v10.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rv1126bp-evb.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rv1126bp-evb-v14.dtb
dtb-$(CONFIG_ARCH_ROCKCHIP) += rv1126bp-evb-v14-dual-cam.dtb
