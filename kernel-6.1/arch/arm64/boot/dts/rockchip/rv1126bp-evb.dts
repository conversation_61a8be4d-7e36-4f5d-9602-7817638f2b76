// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2025 Rockchip Electronics Co., Ltd.
 */

/dts-v1/;

#include "rv1126bp.dtsi"
#include "rv1126bp-evb.dtsi"

/ {
	model = "Rockchip RV1126BP EVB";
	compatible = "rockchip,rv1126bp-evb", "rockchip,rv1126bp";

	chosen {
		stdout-path = "serial2:1500000n8";
	};

	memory@60000000 {
		device_type = "memory";
		reg = <0x0 0x60000000 0x0 0x20000000>;
	};

	dc_12v: dc-12v {
		compatible = "regulator-fixed";
		regulator-name = "dc_12v";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <12000000>;
		regulator-max-microvolt = <12000000>;
	};

	vcc3v3_sys: vcc3v3-sys {
		compatible = "regulator-fixed";
		regulator-name = "vcc3v3_sys";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		vin-supply = <&dc_12v>;
	};

	vcc5v0_sys: vcc5v0-sys {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_sys";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		vin-supply = <&dc_12v>;
	};

	vcc_1v8: vcc-1v8 {
		compatible = "regulator-fixed";
		regulator-name = "vcc_1v8";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		vin-supply = <&vcc3v3_sys>;
	};

	vccio_sd: vccio-sd {
		compatible = "regulator-gpio";
		regulator-name = "vccio_sd";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <3300000>;
		gpios = <&gpio0 RK_PA7 GPIO_ACTIVE_HIGH>;
		states = <1800000 0x1
			  3300000 0x0>;
		vin-supply = <&vcc5v0_sys>;
	};

	sdio_pwrseq: sdio-pwrseq {
		compatible = "mmc-pwrseq-simple";
		clocks = <&rk809 1>;
		clock-names = "ext_clock";
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_enable_h>;
		reset-gpios = <&gpio1 RK_PA0 GPIO_ACTIVE_LOW>;
	};

	wireless-wlan {
		compatible = "wlan-platdata";
		rockchip,grf = <&grf>;
		wifi_chip_type = "ap6xxx";
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_host_wake_irq>;
		WIFI,host_wake_irq = <&gpio1 RK_PA6 GPIO_ACTIVE_HIGH>;
		status = "okay";
	};

	wireless-bluetooth {
		compatible = "bluetooth-platdata";
		clocks = <&rk809 1>;
		clock-names = "ext_clock";
		uart_rts_gpios = <&gpio1 RK_PA4 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default", "rts_gpio";
		pinctrl-0 = <&uart2m0_rtsn>;
		pinctrl-1 = <&uart2_gpios>;
		BT,reset_gpio    = <&gpio1 RK_PA2 GPIO_ACTIVE_HIGH>;
		BT,wake_gpio     = <&gpio1 RK_PA3 GPIO_ACTIVE_HIGH>;
		BT,wake_host_irq = <&gpio1 RK_PA5 GPIO_ACTIVE_HIGH>;
		status = "okay";
	};

	leds {
		compatible = "gpio-leds";

		work_led: work {
			gpios = <&gpio0 RK_PC0 GPIO_ACTIVE_HIGH>;
			linux,default-trigger = "heartbeat";
		};

		diy_led: diy {
			gpios = <&gpio0 RK_PA2 GPIO_ACTIVE_LOW>;
			linux,default-trigger = "mmc1";
			default-state = "off";
		};
	};
};

&cpu0 {
	cpu-supply = <&vdd_arm>;
};

&cpu1 {
	cpu-supply = <&vdd_arm>;
};

&cpu2 {
	cpu-supply = <&vdd_arm>;
};

&cpu3 {
	cpu-supply = <&vdd_arm>;
};

&emmc {
	bus-width = <8>;
	cap-mmc-highspeed;
	mmc-hs200-1_8v;
	supports-emmc;
	non-removable;
	vmmc-supply = <&vcc3v3_sys>;
	vqmmc-supply = <&vcc_1v8>;
	status = "okay";
};

&gmac {
	phy-mode = "rgmii";
	clock_in_out = "output";
	snps,reset-gpio = <&gpio1 RK_PD4 GPIO_ACTIVE_LOW>;
	snps,reset-active-low;
	snps,reset-delays-us = <0 20000 100000>;
	assigned-clocks = <&cru CLK_GMAC_SRC>, <&cru CLK_GMAC_TX_RX>, <&cru CLK_GMAC_ETHERNET_OUT>;
	assigned-clock-parents = <&cru CLK_GMAC_SRC_M1>, <&cru RGMII_MODE_CLK>;
	assigned-clock-rates = <125000000>, <0>, <25000000>;
	pinctrl-names = "default";
	pinctrl-0 = <&rgmiim1_pins>;
	tx_delay = <0x2a>;
	rx_delay = <0x1a>;
	phy-handle = <&rgmii_phy1>;
	status = "okay";

	mdio1 {
		compatible = "snps,dwmac-mdio";
		#address-cells = <1>;
		#size-cells = <0>;

		rgmii_phy1: phy@0 {
			compatible = "ethernet-phy-ieee802.3-c22";
			reg = <0x0>;
		};
	};
};

&i2c0 {
	status = "okay";

	rk809: pmic@20 {
		compatible = "rockchip,rk809";
		reg = <0x20>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PC0 IRQ_TYPE_LEVEL_LOW>;

		pinctrl-names = "default", "pmic-sleep",
				"pmic-power-off", "pmic-reset";
		pinctrl-0 = <&pmic_int>;
		pinctrl-1 = <&soc_slppin_slp>, <&rk817_slppin_slp>;
		pinctrl-2 = <&soc_slppin_gpio>, <&rk817_slppin_pwrdn>;
		pinctrl-3 = <&soc_slppin_gpio>, <&rk817_slppin_rst>;

		rockchip,system-power-controller;
		wakeup-source;
		#clock-cells = <1>;
		clock-output-names = "rk808-clkout1", "rk808-clkout2";

		vcc1-supply = <&vcc3v3_sys>;
		vcc2-supply = <&vcc3v3_sys>;
		vcc3-supply = <&vcc3v3_sys>;
		vcc4-supply = <&vcc3v3_sys>;
		vcc5-supply = <&vcc3v3_sys>;
		vcc6-supply = <&vcc3v3_sys>;
		vcc7-supply = <&vcc3v3_sys>;
		vcc8-supply = <&vcc3v3_sys>;
		vcc9-supply = <&vcc5v0_sys>;

		pwrkey {
			status = "okay";
		};

		pinctrl_rk8xx: pinctrl_rk8xx {
			gpio-controller;
			#gpio-cells = <2>;

			rk817_slppin_null: rk817_slppin_null {
				pins = "gpio_slp";
				function = "pin_fun0";
			};

			rk817_slppin_slp: rk817_slppin_slp {
				pins = "gpio_slp";
				function = "pin_fun1";
			};

			rk817_slppin_pwrdn: rk817_slppin_pwrdn {
				pins = "gpio_slp";
				function = "pin_fun2";
			};

			rk817_slppin_rst: rk817_slppin_rst {
				pins = "gpio_slp";
				function = "pin_fun3";
			};
		};

		regulators {
			vdd_logic: DCDC_REG1 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <500000>;
				regulator-max-microvolt = <1350000>;
				regulator-init-microvolt = <900000>;
				regulator-ramp-delay = <6001>;
				regulator-initial-mode = <0x2>;
				regulator-name = "vdd_logic";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_arm: DCDC_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <500000>;
				regulator-max-microvolt = <1350000>;
				regulator-init-microvolt = <900000>;
				regulator-ramp-delay = <6001>;
				regulator-initial-mode = <0x2>;
				regulator-name = "vdd_arm";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_ddr: DCDC_REG3 {
				regulator-always-on;
				regulator-boot-on;
				regulator-initial-mode = <0x2>;
				regulator-name = "vcc_ddr";
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			vcc3v3_sys: DCDC_REG4 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-initial-mode = <0x2>;
				regulator-name = "vcc3v3_sys";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vcca_1v8: LDO_REG1 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "vcca_1v8";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc1v8_dvp: LDO_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "vcc1v8_dvp";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc1v8_pmu: LDO_REG3 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "vcc1v8_pmu";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vcc_3v0: LDO_REG4 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3000000>;
				regulator-max-microvolt = <3000000>;
				regulator-name = "vcc_3v0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vccio_sd: LDO_REG5 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <3300000>;
				regulator-name = "vccio_sd";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc3v3_sd: LDO_REG6 {
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-name = "vcc3v3_sd";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc2v8_dvp: LDO_REG7 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-name = "vcc2v8_dvp";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc1v8_dvp2: LDO_REG8 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "vcc1v8_dvp2";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc1v8_s3: LDO_REG9 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "vcc1v8_s3";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_1v5: SWITCH_REG1 {
				regulator-always-on;
				regulator-boot-on;
				regulator-name = "vcc_1v5";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc3v3_lcd: SWITCH_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-name = "vcc3v3_lcd";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};
		};
	};
};

&pinctrl {
	pmic {
		pmic_int: pmic_int {
			rockchip,pins =
				<0 RK_PC0 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		soc_slppin_gpio: soc_slppin_gpio {
			rockchip,pins =
				<0 RK_PA4 RK_FUNC_GPIO &pcfg_output_low>;
		};

		soc_slppin_slp: soc_slppin_slp {
			rockchip,pins =
				<0 RK_PA4 1 &pcfg_pull_none>;
		};
	};

	sdio-pwrseq {
		wifi_enable_h: wifi-enable-h {
			rockchip,pins = <1 RK_PA0 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		wifi_host_wake_irq: wifi-host-wake-irq {
			rockchip,pins = <1 RK_PA6 RK_FUNC_GPIO &pcfg_pull_down>;
		};
	};

	wireless-bluetooth {
		uart2_gpios: uart2-gpios {
			rockchip,pins = <1 RK_PA4 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};

&sdmmc0 {
	max-frequency = <150000000>;
	supports-sd;
	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	disable-wp;
	sd-uhs-sdr104;
	vmmc-supply = <&vcc3v3_sd>;
	vqmmc-supply = <&vccio_sd>;
	pinctrl-names = "default";
	pinctrl-0 = <&sdmmc0_clk &sdmmc0_cmd &sdmmc0_bus4 &sdmmc0_det>;
	status = "okay";
};

&sdmmc1 {
	max-frequency = <150000000>;
	supports-sdio;
	bus-width = <4>;
	disable-wp;
	cap-sd-highspeed;
	cap-sdio-irq;
	keep-power-in-suspend;
	mmc-pwrseq = <&sdio_pwrseq>;
	non-removable;
	pinctrl-names = "default";
	pinctrl-0 = <&sdmmc1_clk &sdmmc1_cmd &sdmmc1_bus4>;
	sd-uhs-sdr104;
	status = "okay";
};

&uart2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&uart2m0_xfer &uart2m0_ctsn>;
};

&usb2phy {
	status = "okay";
};

&u2phy_host {
	status = "okay";
};

&u2phy_otg {
	status = "okay";
};

&usb_host_ehci {
	status = "okay";
};

&usb_host_ohci {
	status = "okay";
};

&usbdrd {
	status = "okay";
};
