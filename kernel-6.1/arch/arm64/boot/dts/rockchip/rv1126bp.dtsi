// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2025 Rockchip Electronics Co., Ltd.
 */

#include <dt-bindings/clock/rockchip,rv1126b-cru.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/phy/phy.h>
#include <dt-bindings/pinctrl/rockchip.h>
#include <dt-bindings/power/rockchip,rv1126b-power.h>
#include <dt-bindings/soc/rockchip,boot-mode.h>
#include <dt-bindings/soc/rockchip-system-status.h>
#include <dt-bindings/suspend/rockchip-rv1126b.h>

/ {
	compatible = "rockchip,rv1126bp";

	interrupt-parent = <&gic>;
	#address-cells = <2>;
	#size-cells = <2>;

	aliases {
		ethernet0 = &gmac;
		i2c0 = &i2c0;
		i2c1 = &i2c1;
		i2c2 = &i2c2;
		i2c3 = &i2c3;
		i2c4 = &i2c4;
		i2c5 = &i2c5;
		mmc0 = &emmc;
		mmc1 = &sdmmc0;
		mmc2 = &sdmmc1;
		serial0 = &uart0;
		serial1 = &uart1;
		serial2 = &uart2;
		serial3 = &uart3;
		serial4 = &uart4;
		serial5 = &uart5;
		serial6 = &uart6;
		serial7 = &uart7;
		spi0 = &spi0;
		spi1 = &spi1;
	};

	cpus {
		#address-cells = <2>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0x0 0x0>;
			enable-method = "psci";
			clocks = <&cru ARMCLK>;
			#cooling-cells = <2>;
			dynamic-power-coefficient = <90>;
			operating-points-v2 = <&cpu0_opp_table>;
		};

		cpu1: cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0x0 0x1>;
			enable-method = "psci";
			clocks = <&cru ARMCLK>;
			#cooling-cells = <2>;
			dynamic-power-coefficient = <90>;
			operating-points-v2 = <&cpu0_opp_table>;
		};

		cpu2: cpu@2 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0x0 0x2>;
			enable-method = "psci";
			clocks = <&cru ARMCLK>;
			#cooling-cells = <2>;
			dynamic-power-coefficient = <90>;
			operating-points-v2 = <&cpu0_opp_table>;
		};

		cpu3: cpu@3 {
			device_type = "cpu";
			compatible = "arm,cortex-a7";
			reg = <0x0 0x3>;
			enable-method = "psci";
			clocks = <&cru ARMCLK>;
			#cooling-cells = <2>;
			dynamic-power-coefficient = <90>;
			operating-points-v2 = <&cpu0_opp_table>;
		};
	};

	cpu0_opp_table: cpu0-opp-table {
		compatible = "operating-points-v2";
		opp-shared;

		opp-408000000 {
			opp-hz = /bits/ 64 <408000000>;
			opp-microvolt = <950000 950000 1350000>;
			clock-latency-ns = <40000>;
		};

		opp-600000000 {
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <950000 950000 1350000>;
			clock-latency-ns = <40000>;
		};

		opp-816000000 {
			opp-hz = /bits/ 64 <816000000>;
			opp-microvolt = <1000000 1000000 1350000>;
			clock-latency-ns = <40000>;
		};

		opp-1008000000 {
			opp-hz = /bits/ 64 <1008000000>;
			opp-microvolt = <1100000 1100000 1350000>;
			clock-latency-ns = <40000>;
		};

		opp-1200000000 {
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <1225000 1225000 1350000>;
			clock-latency-ns = <40000>;
		};

		opp-1416000000 {
			opp-hz = /bits/ 64 <1416000000>;
			opp-microvolt = <1300000 1300000 1350000>;
			clock-latency-ns = <40000>;
		};

		opp-1512000000 {
			opp-hz = /bits/ 64 <1512000000>;
			opp-microvolt = <1350000 1350000 1350000>;
			clock-latency-ns = <40000>;
		};
	};

	arm-pmu {
		compatible = "arm,cortex-a7-pmu";
		interrupts = <GIC_SPI 123 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 124 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 125 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 126 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-affinity = <&cpu0>, <&cpu1>, <&cpu2>, <&cpu3>;
	};

	psci {
		compatible = "arm,psci-1.0";
		method = "smc";
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>;
		arm,cpu-registers-not-fw-configured;
	};

	xin24m: xin24m {
		compatible = "fixed-clock";
		clock-frequency = <24000000>;
		clock-output-names = "xin24m";
		#clock-cells = <0>;
	};

	xin32k: xin32k {
		compatible = "fixed-clock";
		clock-frequency = <32768>;
		clock-output-names = "xin32k";
		pinctrl-0 = <&clk_32k_out>;
		pinctrl-names = "default";
		#clock-cells = <0>;
	};

	sram: sram@10f00000 {
		compatible = "mmio-sram";
		reg = <0x0 0x10f00000 0x0 0x100000>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x0 0x10f00000 0x100000>;

		/* 4k reserved for ARM Trusted Firmware usage */
		scmi_shmem: sram@0 {
			compatible = "arm,scmi-shmem";
			reg = <0x0 0x400>;
		};
	};

	scmi: scmi {
		compatible = "arm,scmi-smc";
		arm,smc-id = <0x82000010>;
		shmem = <&scmi_shmem>;
		#address-cells = <1>;
		#size-cells = <0>;

		scmi_clk: protocol@14 {
			reg = <0x14>;
			#clock-cells = <1>;
		};

		scmi_reset: protocol@16 {
			reg = <0x16>;
			#reset-cells = <1>;
		};
	};

	gic: interrupt-controller@10300000 {
		compatible = "arm,gic-400";
		interrupt-controller;
		#interrupt-cells = <3>;
		#address-cells = <0>;

		reg = <0x0 0x10301000 0x0 0x1000>,
		      <0x0 0x10302000 0x0 0x2000>,
		      <0x0 0x10304000 0x0 0x2000>,
		      <0x0 0x10306000 0x0 0x2000>;
		interrupts = <GIC_PPI 9 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>;
	};

	pmu: power-management@20020000 {
		compatible = "rockchip,rv1126bp-pmu", "syscon", "simple-mfd";
		reg = <0x0 0x20020000 0x0 0x1000>;

		power: power-controller {
			compatible = "rockchip,rv1126bp-power-controller";
			#power-domain-cells = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			power-domain@RV1126_PD_VEPU {
				reg = <RV1126_PD_VEPU>;
				clocks = <&cru ACLK_JENC>, <&cru HCLK_JENC>,
					 <&cru ACLK_VENC>, <&cru HCLK_VENC>,
					 <&cru CLK_VENC_CORE>;
				pm_qos = <&qos_venc>, <&qos_jpeg>;
				#power-domain-cells = <0>;
			};

			power-domain@RV1126_PD_VDPU {
				reg = <RV1126_PD_VDPU>;
				clocks = <&cru ACLK_VDEC>, <&cru HCLK_VDEC>,
					 <&cru CLK_VDEC_CORE>, <&cru CLK_VDEC_CA>,
					 <&cru CLK_VDEC_HEVC_CA>;
				pm_qos = <&qos_vdec>;
				#power-domain-cells = <0>;
			};

			power-domain@RV1126_PD_ISPP {
				reg = <RV1126_PD_ISPP>;
				clocks = <&cru CLK_ISPP>, <&cru ACLK_ISPP>,
					 <&cru HCLK_ISPP>;
				pm_qos = <&qos_ispp_m0>, <&qos_ispp_m1>;
				#power-domain-cells = <0>;
			};

			power-domain@RV1126_PD_VIPOUT {
				reg = <RV1126_PD_VIPOUT>;
				clocks = <&cru PCLK_CSIHOST>, <&cru ACLK_CIF>,
					 <&cru HCLK_CIF>, <&cru DCLK_CIF>,
					 <&cru ACLK_ISP>, <&cru HCLK_ISP>,
					 <&cru CLK_ISP>;
				pm_qos = <&qos_isp>, <&qos_vicap>;
				#power-domain-cells = <0>;
			};

			power-domain@RV1126_PD_NVM {
				reg = <RV1126_PD_NVM>;
				clocks = <&cru HCLK_EMMC>, <&cru CLK_EMMC>,
					 <&cru HCLK_SFC>, <&cru HCLK_SFC_XIP>,
					 <&cru SCLK_SFC>;
				pm_qos = <&qos_emmc>, <&qos_nand>, <&qos_sfc>;
				#power-domain-cells = <0>;
			};

			power-domain@RV1126_PD_SDIO {
				reg = <RV1126_PD_SDIO>;
				clocks = <&cru HCLK_SDMMC>, <&cru CLK_SDMMC>,
					 <&cru HCLK_SDIO>, <&cru CLK_SDIO>;
				pm_qos = <&qos_sdio>;
				#power-domain-cells = <0>;
			};

			power-domain@RV1126_PD_USB {
				reg = <RV1126_PD_USB>;
				clocks = <&cru HCLK_HOST>, <&cru HCLK_HOST_ARB>,
					 <&cru HCLK_OTG>, <&cru CLK_OTG_ADP>;
				pm_qos = <&qos_usb_host>, <&qos_usb_otg>;
				#power-domain-cells = <0>;
			};
		};
	};

	firmware {
		optee {
			compatible = "linaro,optee-tz";
			method = "smc";
		};
	};

	display_subsystem: display-subsystem {
		compatible = "rockchip,display-subsystem";
		ports = <&vop_out>;
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;

		usb2phy_grf: syscon@10008000 {
			compatible = "rockchip,rv1126bp-usb2phy-grf", "syscon";
			reg = <0x0 0x10008000 0x0 0x1000>;
		};

		grf: syscon@10300000 {
			compatible = "rockchip,rv1126bp-grf", "syscon", "simple-mfd";
			reg = <0x0 0x10300000 0x0 0x1000>;

			io_domains: io-domains {
				compatible = "rockchip,rv1126bp-io-voltage-domain";
				status = "disabled";
			};
		};

		qos_emmc: qos@1ac00000 {
			compatible = "rockchip,rv1126bp-qos", "syscon";
			reg = <0x0 0x1ac00000 0x0 0x20>;
		};

		qos_nand: qos@1ac00080 {
			compatible = "rockchip,rv1126bp-qos", "syscon";
			reg = <0x0 0x1ac00080 0x0 0x20>;
		};

		qos_sfc: qos@1ac00200 {
			compatible = "rockchip,rv1126bp-qos", "syscon";
			reg = <0x0 0x1ac00200 0x0 0x20>;
		};

		qos_sdio: qos@1ac00400 {
			compatible = "rockchip,rv1126bp-qos", "syscon";
			reg = <0x0 0x1ac00400 0x0 0x20>;
		};

		qos_usb_host: qos@1ac00480 {
			compatible = "rockchip,rv1126bp-qos", "syscon";
			reg = <0x0 0x1ac00480 0x0 0x20>;
		};

		qos_usb_otg: qos@1ac00500 {
			compatible = "rockchip,rv1126bp-qos", "syscon";
			reg = <0x0 0x1ac00500 0x0 0x20>;
		};

		qos_isp: qos@1ac00680 {
			compatible = "rockchip,rv1126bp-qos", "syscon";
			reg = <0x0 0x1ac00680 0x0 0x20>;
		};

		qos_vicap: qos@1ac00700 {
			compatible = "rockchip,rv1126bp-qos", "syscon";
			reg = <0x0 0x1ac00700 0x0 0x20>;
		};

		qos_venc: qos@1ac00780 {
			compatible = "rockchip,rv1126bp-qos", "syscon";
			reg = <0x0 0x1ac00780 0x0 0x20>;
		};

		qos_jpeg: qos@1ac00800 {
			compatible = "rockchip,rv1126bp-qos", "syscon";
			reg = <0x0 0x1ac00800 0x0 0x20>;
		};

		qos_vdec: qos@1ac00880 {
			compatible = "rockchip,rv1126bp-qos", "syscon";
			reg = <0x0 0x1ac00880 0x0 0x20>;
		};

		qos_ispp_m0: qos@1ac00900 {
			compatible = "rockchip,rv1126bp-qos", "syscon";
			reg = <0x0 0x1ac00900 0x0 0x20>;
		};

		qos_ispp_m1: qos@1ac00980 {
			compatible = "rockchip,rv1126bp-qos", "syscon";
			reg = <0x0 0x1ac00980 0x0 0x20>;
		};

		cru: clock-controller@20200000 {
			compatible = "rockchip,rv1126bp-cru";
			reg = <0x0 0x20200000 0x0 0x1000>;
			clocks = <&xin24m>;
			clock-names = "xin24m";
			rockchip,grf = <&grf>;
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		i2c0: i2c@******** {
			compatible = "rockchip,rv1126bp-i2c", "rockchip,rk3399-i2c";
			reg = <0x0 0x******** 0x0 0x1000>;
			interrupts = <GIC_SPI 9 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&cru CLK_I2C0>, <&cru PCLK_I2C0>;
			clock-names = "i2c", "pclk";
			pinctrl-names = "default";
			pinctrl-0 = <&i2c0m0_xfer>;
			status = "disabled";
		};

		i2c1: i2c@******** {
			compatible = "rockchip,rv1126bp-i2c", "rockchip,rk3399-i2c";
			reg = <0x0 0x******** 0x0 0x1000>;
			interrupts = <GIC_SPI 10 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&cru CLK_I2C1>, <&cru PCLK_I2C1>;
			clock-names = "i2c", "pclk";
			pinctrl-names = "default";
			pinctrl-0 = <&i2c1m0_xfer>;
			status = "disabled";
		};

		i2c2: i2c@******** {
			compatible = "rockchip,rv1126bp-i2c", "rockchip,rk3399-i2c";
			reg = <0x0 0x******** 0x0 0x1000>;
			interrupts = <GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&cru CLK_I2C2>, <&cru PCLK_I2C2>;
			clock-names = "i2c", "pclk";
			pinctrl-names = "default";
			pinctrl-0 = <&i2c2m0_xfer>;
			status = "disabled";
		};

		i2c3: i2c@******** {
			compatible = "rockchip,rv1126bp-i2c", "rockchip,rk3399-i2c";
			reg = <0x0 0x******** 0x0 0x1000>;
			interrupts = <GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&cru CLK_I2C3>, <&cru PCLK_I2C3>;
			clock-names = "i2c", "pclk";
			pinctrl-names = "default";
			pinctrl-0 = <&i2c3m0_xfer>;
			status = "disabled";
		};

		i2c4: i2c@******** {
			compatible = "rockchip,rv1126bp-i2c", "rockchip,rk3399-i2c";
			reg = <0x0 0x******** 0x0 0x1000>;
			interrupts = <GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&cru CLK_I2C4>, <&cru PCLK_I2C4>;
			clock-names = "i2c", "pclk";
			pinctrl-names = "default";
			pinctrl-0 = <&i2c4m0_xfer>;
			status = "disabled";
		};

		i2c5: i2c@******** {
			compatible = "rockchip,rv1126bp-i2c", "rockchip,rk3399-i2c";
			reg = <0x0 0x******** 0x0 0x1000>;
			interrupts = <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&cru CLK_I2C5>, <&cru PCLK_I2C5>;
			clock-names = "i2c", "pclk";
			pinctrl-names = "default";
			pinctrl-0 = <&i2c5m0_xfer>;
			status = "disabled";
		};

		uart0: serial@20300000 {
			compatible = "rockchip,rv1126bp-uart", "snps,dw-apb-uart";
			reg = <0x0 0x20300000 0x0 0x100>;
			interrupts = <GIC_SPI 15 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru SCLK_UART0>, <&cru PCLK_UART0>;
			clock-names = "baudclk", "apb_pclk";
			reg-shift = <2>;
			reg-io-width = <4>;
			dmas = <&dmac 0>, <&dmac 1>;
			dma-names = "tx", "rx";
			pinctrl-names = "default";
			pinctrl-0 = <&uart0m0_xfer>;
			status = "disabled";
		};

		uart1: serial@20310000 {
			compatible = "rockchip,rv1126bp-uart", "snps,dw-apb-uart";
			reg = <0x0 0x20310000 0x0 0x100>;
			interrupts = <GIC_SPI 16 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru SCLK_UART1>, <&cru PCLK_UART1>;
			clock-names = "baudclk", "apb_pclk";
			reg-shift = <2>;
			reg-io-width = <4>;
			dmas = <&dmac 2>, <&dmac 3>;
			dma-names = "tx", "rx";
			pinctrl-names = "default";
			pinctrl-0 = <&uart1m0_xfer>;
			status = "disabled";
		};

		uart2: serial@20320000 {
			compatible = "rockchip,rv1126bp-uart", "snps,dw-apb-uart";
			reg = <0x0 0x20320000 0x0 0x100>;
			interrupts = <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru SCLK_UART2>, <&cru PCLK_UART2>;
			clock-names = "baudclk", "apb_pclk";
			reg-shift = <2>;
			reg-io-width = <4>;
			dmas = <&dmac 4>, <&dmac 5>;
			dma-names = "tx", "rx";
			pinctrl-names = "default";
			pinctrl-0 = <&uart2m0_xfer>;
			status = "disabled";
		};

		uart3: serial@20330000 {
			compatible = "rockchip,rv1126bp-uart", "snps,dw-apb-uart";
			reg = <0x0 0x20330000 0x0 0x100>;
			interrupts = <GIC_SPI 18 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru SCLK_UART3>, <&cru PCLK_UART3>;
			clock-names = "baudclk", "apb_pclk";
			reg-shift = <2>;
			reg-io-width = <4>;
			dmas = <&dmac 6>, <&dmac 7>;
			dma-names = "tx", "rx";
			pinctrl-names = "default";
			pinctrl-0 = <&uart3m0_xfer>;
			status = "disabled";
		};

		uart4: serial@20340000 {
			compatible = "rockchip,rv1126bp-uart", "snps,dw-apb-uart";
			reg = <0x0 0x20340000 0x0 0x100>;
			interrupts = <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru SCLK_UART4>, <&cru PCLK_UART4>;
			clock-names = "baudclk", "apb_pclk";
			reg-shift = <2>;
			reg-io-width = <4>;
			dmas = <&dmac 8>, <&dmac 9>;
			dma-names = "tx", "rx";
			pinctrl-names = "default";
			pinctrl-0 = <&uart4m0_xfer>;
			status = "disabled";
		};

		uart5: serial@20350000 {
			compatible = "rockchip,rv1126bp-uart", "snps,dw-apb-uart";
			reg = <0x0 0x20350000 0x0 0x100>;
			interrupts = <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru SCLK_UART5>, <&cru PCLK_UART5>;
			clock-names = "baudclk", "apb_pclk";
			reg-shift = <2>;
			reg-io-width = <4>;
			dmas = <&dmac 10>, <&dmac 11>;
			dma-names = "tx", "rx";
			pinctrl-names = "default";
			pinctrl-0 = <&uart5m0_xfer>;
			status = "disabled";
		};

		uart6: serial@20360000 {
			compatible = "rockchip,rv1126bp-uart", "snps,dw-apb-uart";
			reg = <0x0 0x20360000 0x0 0x100>;
			interrupts = <GIC_SPI 21 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru SCLK_UART6>, <&cru PCLK_UART6>;
			clock-names = "baudclk", "apb_pclk";
			reg-shift = <2>;
			reg-io-width = <4>;
			dmas = <&dmac 12>, <&dmac 13>;
			dma-names = "tx", "rx";
			pinctrl-names = "default";
			pinctrl-0 = <&uart6m0_xfer>;
			status = "disabled";
		};

		uart7: serial@20370000 {
			compatible = "rockchip,rv1126bp-uart", "snps,dw-apb-uart";
			reg = <0x0 0x20370000 0x0 0x100>;
			interrupts = <GIC_SPI 22 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru SCLK_UART7>, <&cru PCLK_UART7>;
			clock-names = "baudclk", "apb_pclk";
			reg-shift = <2>;
			reg-io-width = <4>;
			dmas = <&dmac 14>, <&dmac 15>;
			dma-names = "tx", "rx";
			pinctrl-names = "default";
			pinctrl-0 = <&uart7m0_xfer>;
			status = "disabled";
		};

		spi0: spi@20380000 {
			compatible = "rockchip,rv1126bp-spi", "rockchip,rk3066-spi";
			reg = <0x0 0x20380000 0x0 0x1000>;
			interrupts = <GIC_SPI 23 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&cru CLK_SPI0>, <&cru PCLK_SPI0>;
			clock-names = "spiclk", "apb_pclk";
			dmas = <&dmac 16>, <&dmac 17>;
			dma-names = "tx", "rx";
			pinctrl-names = "default";
			pinctrl-0 = <&spi0m0_clk &spi0m0_csn0 &spi0m0_miso &spi0m0_mosi>;
			status = "disabled";
		};

		spi1: spi@20390000 {
			compatible = "rockchip,rv1126bp-spi", "rockchip,rk3066-spi";
			reg = <0x0 0x20390000 0x0 0x1000>;
			interrupts = <GIC_SPI 24 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <1>;
			#size-cells = <0>;
			clocks = <&cru CLK_SPI1>, <&cru PCLK_SPI1>;
			clock-names = "spiclk", "apb_pclk";
			dmas = <&dmac 18>, <&dmac 19>;
			dma-names = "tx", "rx";
			pinctrl-names = "default";
			pinctrl-0 = <&spi1m0_clk &spi1m0_csn0 &spi1m0_miso &spi1m0_mosi>;
			status = "disabled";
		};

		dmac: dmac@203a0000 {
			compatible = "arm,pl330", "arm,primecell";
			reg = <0x0 0x203a0000 0x0 0x4000>;
			interrupts = <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 26 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru ACLK_DMAC>;
			clock-names = "apb_pclk";
			#dma-cells = <1>;
		};

		emmc: mmc@21020000 {
			compatible = "rockchip,rv1126bp-dw-mshc", "rockchip,rk3288-dw-mshc";
			reg = <0x0 0x21020000 0x0 0x4000>;
			interrupts = <GIC_SPI 85 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru HCLK_EMMC>, <&cru CLK_EMMC>,
				 <&cru SCLK_EMMC_DRV>, <&cru SCLK_EMMC_SAMPLE>;
			clock-names = "biu", "ciu", "ciu-drive", "ciu-sample";
			fifo-depth = <0x100>;
			max-frequency = <200000000>;
			power-domains = <&power RV1126_PD_NVM>;
			status = "disabled";
		};

		sdmmc0: mmc@21030000 {
			compatible = "rockchip,rv1126bp-dw-mshc", "rockchip,rk3288-dw-mshc";
			reg = <0x0 0x21030000 0x0 0x4000>;
			interrupts = <GIC_SPI 86 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru HCLK_SDMMC>, <&cru CLK_SDMMC>,
				 <&cru SCLK_SDMMC_DRV>, <&cru SCLK_SDMMC_SAMPLE>;
			clock-names = "biu", "ciu", "ciu-drive", "ciu-sample";
			fifo-depth = <0x100>;
			max-frequency = <150000000>;
			pinctrl-names = "default";
			pinctrl-0 = <&sdmmc0_clk &sdmmc0_cmd &sdmmc0_bus4 &sdmmc0_det>;
			status = "disabled";
		};

		sdmmc1: mmc@21040000 {
			compatible = "rockchip,rv1126bp-dw-mshc", "rockchip,rk3288-dw-mshc";
			reg = <0x0 0x21040000 0x0 0x4000>;
			interrupts = <GIC_SPI 87 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru HCLK_SDIO>, <&cru CLK_SDIO>,
				 <&cru SCLK_SDIO_DRV>, <&cru SCLK_SDIO_SAMPLE>;
			clock-names = "biu", "ciu", "ciu-drive", "ciu-sample";
			fifo-depth = <0x100>;
			max-frequency = <150000000>;
			pinctrl-names = "default";
			pinctrl-0 = <&sdmmc1_clk &sdmmc1_cmd &sdmmc1_bus4>;
			power-domains = <&power RV1126_PD_SDIO>;
			status = "disabled";
		};

		sfc: spi@21050000 {
			compatible = "rockchip,sfc";
			reg = <0x0 0x21050000 0x0 0x4000>;
			interrupts = <GIC_SPI 88 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru SCLK_SFC>, <&cru HCLK_SFC>;
			clock-names = "clk_sfc", "hclk_sfc";
			power-domains = <&power RV1126_PD_NVM>;
			status = "disabled";
		};

		gmac: ethernet@21060000 {
			compatible = "rockchip,rv1126bp-gmac", "snps,dwmac-4.20a";
			reg = <0x0 0x21060000 0x0 0x10000>;
			interrupts = <GIC_SPI 89 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 90 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "macirq", "eth_wake_irq";
			clocks = <&cru CLK_GMAC_SRC>, <&cru CLK_GMAC_TX_RX>,
				 <&cru CLK_GMAC_TX_RX>, <&cru CLK_GMAC_REF>,
				 <&cru ACLK_GMAC>, <&cru PCLK_GMAC>,
				 <&cru CLK_GMAC_TX>, <&cru CLK_GMAC_RX>;
			clock-names = "stmmaceth", "mac_clk_tx",
				      "mac_clk_rx", "clk_mac_ref",
				      "aclk_mac", "pclk_mac",
				      "clk_mac_speed", "clk_mac_speed_rx";
			resets = <&cru SRST_GMAC_A>;
			reset-names = "stmmaceth";
			rockchip,grf = <&grf>;
			snps,txpbl = <0x4>;
			status = "disabled";
		};

		usb2phy: usb2-phy@21070000 {
			compatible = "rockchip,rv1126bp-usb2phy";
			reg = <0x0 0x21070000 0x0 0x10000>;
			clocks = <&cru CLK_USBPHY_REF>;
			clock-names = "phyclk";
			#clock-cells = <0>;
			assigned-clocks = <&cru USB480M>;
			assigned-clock-parents = <&usb2phy>;
			clock-output-names = "usb480m_phy";
			rockchip,usbgrf = <&usb2phy_grf>;
			status = "disabled";

			u2phy_host: host-port {
				#phy-cells = <0>;
				interrupts = <GIC_SPI 91 IRQ_TYPE_LEVEL_HIGH>;
				interrupt-names = "linestate";
				status = "disabled";
			};

			u2phy_otg: otg-port {
				#phy-cells = <0>;
				interrupts = <GIC_SPI 92 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 93 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 94 IRQ_TYPE_LEVEL_HIGH>;
				interrupt-names = "otg-bvalid", "otg-id", "linestate";
				status = "disabled";
			};
		};

		usb_host_ehci: usb@21080000 {
			compatible = "generic-ehci";
			reg = <0x0 0x21080000 0x0 0x20000>;
			interrupts = <GIC_SPI 95 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru HCLK_HOST>, <&cru HCLK_HOST_ARB>,
				 <&cru CLK_USBPHY_REF>;
			phys = <&u2phy_host>;
			phy-names = "usb";
			power-domains = <&power RV1126_PD_USB>;
			status = "disabled";
		};

		usb_host_ohci: usb@210a0000 {
			compatible = "generic-ohci";
			reg = <0x0 0x210a0000 0x0 0x20000>;
			interrupts = <GIC_SPI 96 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru HCLK_HOST>, <&cru HCLK_HOST_ARB>,
				 <&cru CLK_USBPHY_REF>;
			phys = <&u2phy_host>;
			phy-names = "usb";
			power-domains = <&power RV1126_PD_USB>;
			status = "disabled";
		};

		usbdrd: usb@210c0000 {
			compatible = "rockchip,rv1126bp-dwc3", "snps,dwc3";
			reg = <0x0 0x210c0000 0x0 0x400000>;
			interrupts = <GIC_SPI 97 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru CLK_USB3OTG0_REF>, <&cru CLK_USB3OTG0_SUSPEND>,
				 <&cru ACLK_USB3OTG0>;
			clock-names = "ref_clk", "suspend_clk", "bus_clk";
			dr_mode = "otg";
			phys = <&u2phy_otg>;
			phy-names = "usb2-phy";
			power-domains = <&power RV1126_PD_USB>;
			status = "disabled";
		};



		pinctrl: pinctrl {
			compatible = "rockchip,rv1126bp-pinctrl";
			rockchip,grf = <&grf>;
			#address-cells = <2>;
			#size-cells = <2>;
			ranges;

			gpio0: gpio0@******** {
				compatible = "rockchip,gpio-bank";
				reg = <0x0 0x******** 0x0 0x100>;
				interrupts = <GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&cru PCLK_GPIO0>, <&cru DBCLK_GPIO0>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
			};

			gpio1: gpio1@******** {
				compatible = "rockchip,gpio-bank";
				reg = <0x0 0x******** 0x0 0x100>;
				interrupts = <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&cru PCLK_GPIO1>, <&cru DBCLK_GPIO1>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
			};

			gpio2: gpio2@******** {
				compatible = "rockchip,gpio-bank";
				reg = <0x0 0x******** 0x0 0x100>;
				interrupts = <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&cru PCLK_GPIO2>, <&cru DBCLK_GPIO2>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
			};

			gpio3: gpio3@******** {
				compatible = "rockchip,gpio-bank";
				reg = <0x0 0x******** 0x0 0x100>;
				interrupts = <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&cru PCLK_GPIO3>, <&cru DBCLK_GPIO3>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
			};

			gpio4: gpio4@******** {
				compatible = "rockchip,gpio-bank";
				reg = <0x0 0x******** 0x0 0x100>;
				interrupts = <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&cru PCLK_GPIO4>, <&cru DBCLK_GPIO4>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
			};

			gpio5: gpio5@******** {
				compatible = "rockchip,gpio-bank";
				reg = <0x0 0x******** 0x0 0x100>;
				interrupts = <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&cru PCLK_GPIO5>, <&cru DBCLK_GPIO5>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
			};

			gpio6: gpio6@******** {
				compatible = "rockchip,gpio-bank";
				reg = <0x0 0x******** 0x0 0x100>;
				interrupts = <GIC_SPI 9 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&cru PCLK_GPIO6>, <&cru DBCLK_GPIO6>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
			};

			gpio7: gpio7@202a0000 {
				compatible = "rockchip,gpio-bank";
				reg = <0x0 0x202a0000 0x0 0x100>;
				interrupts = <GIC_SPI 10 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&cru PCLK_GPIO7>, <&cru DBCLK_GPIO7>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
			};
		};
	};
};

#include "rv1126bp-pinctrl.dtsi"
