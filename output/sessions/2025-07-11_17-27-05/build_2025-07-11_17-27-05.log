# 2025-07-11 17:27:05
# run hook: build kernel

[36mToolchain for kernel:[0m
[36m/work/rv1126pb/rv1126b_linux6.1_release/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-[0m

[36m==========================================[0m
[36m          Start building kernel[0m
[36m==========================================[0m
[35m+ make -C /work/rv1126pb/rv1126b_linux6.1_release/kernel/ -j23 CROSS_COMPILE=/work/rv1126pb/rv1126b_linux6.1_release/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rv1126b_defconfig[0m
make: Entering directory '/work/rv1126pb/rv1126b_linux6.1_release/kernel-6.1'
arch/arm64/configs/rv1126b_defconfig:339:warning: override: reassigning to symbol EXT4_FS
#
# No change to .config
#
make: Leaving directory '/work/rv1126pb/rv1126b_linux6.1_release/kernel-6.1'
[35m+ make -C /work/rv1126pb/rv1126b_linux6.1_release/kernel/ -j23 CROSS_COMPILE=/work/rv1126pb/rv1126b_linux6.1_release/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rv1126bp-evb-v14.img[0m
make: Entering directory '/work/rv1126pb/rv1126b_linux6.1_release/kernel-6.1'
  SYNC    include/config/auto.conf.cmd
  UPD     include/generated/compile.h
  DTC     arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtb
Error: arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dts:1.1-2 syntax error
FATAL ERROR: Unable to parse input tree
make[3]: *** [scripts/Makefile.lib:423：arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtb] 错误 1
make[2]: *** [scripts/Makefile.build:503：arch/arm64/boot/dts/rockchip] 错误 2
make[1]: *** [Makefile:1466：rockchip/rv1126bp-evb-v14.dtb] 错误 2
make[1]: *** 正在等待未完成的任务....
  CC      scripts/mod/empty.o
  CC      scripts/mod/devicetable-offsets.s
  MKELF   scripts/mod/elfconfig.h
  HOSTCC  scripts/mod/modpost.o
  HOSTCC  scripts/mod/sumversion.o
  HOSTCC  scripts/mod/file2alias.o
  HOSTLD  scripts/mod/modpost
  CC      kernel/bounds.s
  CC      arch/arm64/kernel/asm-offsets.s
  CALL    scripts/checksyscalls.sh
make: *** [arch/arm64/Makefile:221: rv1126bp-evb-v14.img] Error 2
make: Leaving directory '/work/rv1126pb/rv1126b_linux6.1_release/kernel-6.1'
[31mERROR: Running /work/rv1126pb/rv1126b_linux6.1_release/device/rockchip/common/build-hooks/10-kernel.sh - run_command failed![0m
[31mERROR: exit code 2 from line 36:[0m
[31m    $@[0m
[31mERROR: call stack:[0m
[31m    build-helper: run_command(36)[0m
[31m    10-kernel.sh: do_build(79)[0m
[31m    10-kernel.sh: build_hook(438)[0m
[31m    build-helper: try_func(63)[0m
[31m    build-helper: try_hook(96)[0m
[31m    build-helper: source(165)[0m
[31m    10-kernel.sh: main(490)[0m
[31mERROR: Running /work/rv1126pb/rv1126b_linux6.1_release/device/rockchip/common/build-hooks/10-kernel.sh - try_func build_hook kernel failed![0m
[31mERROR: exit code 2 from line 67:[0m
[31m    build_hook[0m
[31mERROR: call stack:[0m
[31m    build-helper: try_func(67)[0m
[31m    build-helper: try_hook(96)[0m
[31m    build-helper: source(165)[0m
[31m    10-kernel.sh: main(490)[0m
