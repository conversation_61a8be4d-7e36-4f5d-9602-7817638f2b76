#!/usr/bin/env python3
"""
RV1126 to RV1126BP GPIO Pin Mapping Converter
Based on the pin mapping table provided in the image
"""

# GPIO pin mapping from RV1126 to RV1126BP
# Format: (original_gpio_bank, original_pin) -> (new_gpio_bank, new_pin)
PIN_MAPPING = {
    # VCCIO4 mappings
    (0, 'RK_PA0'): (1, 'RK_PA0'),  # GPIO0_A0 -> GPIO1_A0
    (0, 'RK_PA1'): (1, 'RK_PA1'),  # GPIO0_A1 -> GPIO1_A1
    (0, 'RK_PA2'): (1, 'RK_PA2'),  # GPIO0_A2 -> GPIO1_A2
    (0, 'RK_PA3'): (1, 'RK_PA3'),  # GPIO0_A3 -> GPIO1_A3
    (0, 'RK_PA4'): (1, 'RK_PA4'),  # GPIO0_A4 -> GPIO1_A4
    (0, 'RK_PA5'): (1, 'RK_PA5'),  # GPIO0_A5 -> GPIO1_A5
    (0, 'RK_PA6'): (1, 'RK_PA6'),  # GPIO0_A6 -> GPIO1_A6
    (0, 'RK_PA7'): (1, 'RK_PA7'),  # GPIO0_A7 -> GPIO1_A7
    (0, 'RK_PB0'): (1, 'RK_PB0'),  # GPIO0_B0 -> GPIO1_B0
    (0, 'RK_PB1'): (1, 'RK_PB1'),  # GPIO0_B1 -> GPIO1_B1
    (0, 'RK_PB2'): (1, 'RK_PB2'),  # GPIO0_B2 -> GPIO1_B2
    (0, 'RK_PB3'): (1, 'RK_PB3'),  # GPIO0_B3 -> GPIO1_B3
    (0, 'RK_PB4'): (1, 'RK_PB4'),  # GPIO0_B4 -> GPIO1_B4
    (0, 'RK_PB5'): (1, 'RK_PB5'),  # GPIO0_B5 -> GPIO1_B5
    (0, 'RK_PB6'): (1, 'RK_PB6'),  # GPIO0_B6 -> GPIO1_B6
    (0, 'RK_PB7'): (1, 'RK_PB7'),  # GPIO0_B7 -> GPIO1_B7
    (0, 'RK_PC0'): (1, 'RK_PC0'),  # GPIO0_C0 -> GPIO1_C0
    (0, 'RK_PC1'): (1, 'RK_PC1'),  # GPIO0_C1 -> GPIO1_C1
    (0, 'RK_PC2'): (1, 'RK_PC2'),  # GPIO0_C2 -> GPIO1_C2
    (0, 'RK_PC3'): (1, 'RK_PC3'),  # GPIO0_C3 -> GPIO1_C3
    (0, 'RK_PC4'): (1, 'RK_PC4'),  # GPIO0_C4 -> GPIO1_C4
    (0, 'RK_PC5'): (1, 'RK_PC5'),  # GPIO0_C5 -> GPIO1_C5
    (0, 'RK_PC6'): (1, 'RK_PC6'),  # GPIO0_C6 -> GPIO1_C6
    (0, 'RK_PC7'): (1, 'RK_PC7'),  # GPIO0_C7 -> GPIO1_C7
    (0, 'RK_PD0'): (1, 'RK_PD0'),  # GPIO0_D0 -> GPIO1_D0
    (0, 'RK_PD1'): (1, 'RK_PD1'),  # GPIO0_D1 -> GPIO1_D1
    (0, 'RK_PD2'): (1, 'RK_PD2'),  # GPIO0_D2 -> GPIO1_D2
    (0, 'RK_PD3'): (1, 'RK_PD3'),  # GPIO0_D3 -> GPIO1_D3
    (0, 'RK_PD4'): (1, 'RK_PD4'),  # GPIO0_D4 -> GPIO1_D4
    (0, 'RK_PD5'): (1, 'RK_PD5'),  # GPIO0_D5 -> GPIO1_D5
    (0, 'RK_PD6'): (1, 'RK_PD6'),  # GPIO0_D6 -> GPIO1_D6
    (0, 'RK_PD7'): (1, 'RK_PD7'),  # GPIO0_D7 -> GPIO1_D7
    
    # VCCIO5 mappings
    (1, 'RK_PA0'): (2, 'RK_PA0'),  # GPIO1_A0 -> GPIO2_A0
    (1, 'RK_PA1'): (2, 'RK_PA1'),  # GPIO1_A1 -> GPIO2_A1
    (1, 'RK_PA2'): (2, 'RK_PA2'),  # GPIO1_A2 -> GPIO2_A2
    (1, 'RK_PA3'): (2, 'RK_PA3'),  # GPIO1_A3 -> GPIO2_A3
    (1, 'RK_PA4'): (2, 'RK_PA4'),  # GPIO1_A4 -> GPIO2_A4
    (1, 'RK_PA5'): (2, 'RK_PA5'),  # GPIO1_A5 -> GPIO2_A5
    (1, 'RK_PA6'): (2, 'RK_PA6'),  # GPIO1_A6 -> GPIO2_A6
    (1, 'RK_PA7'): (2, 'RK_PA7'),  # GPIO1_A7 -> GPIO2_A7
    (1, 'RK_PB0'): (2, 'RK_PB0'),  # GPIO1_B0 -> GPIO2_B0
    (1, 'RK_PB1'): (2, 'RK_PB1'),  # GPIO1_B1 -> GPIO2_B1
    (1, 'RK_PB2'): (2, 'RK_PB2'),  # GPIO1_B2 -> GPIO2_B2
    (1, 'RK_PB3'): (2, 'RK_PB3'),  # GPIO1_B3 -> GPIO2_B3
    (1, 'RK_PB4'): (2, 'RK_PB4'),  # GPIO1_B4 -> GPIO2_B4
    (1, 'RK_PB5'): (2, 'RK_PB5'),  # GPIO1_B5 -> GPIO2_B5
    (1, 'RK_PB6'): (2, 'RK_PB6'),  # GPIO1_B6 -> GPIO2_B6
    (1, 'RK_PB7'): (2, 'RK_PB7'),  # GPIO1_B7 -> GPIO2_B7
    
    # VCCIO6 mappings
    (2, 'RK_PA0'): (3, 'RK_PA0'),  # GPIO2_A0 -> GPIO3_A0
    (2, 'RK_PA1'): (3, 'RK_PA1'),  # GPIO2_A1 -> GPIO3_A1
    (2, 'RK_PA2'): (3, 'RK_PA2'),  # GPIO2_A2 -> GPIO3_A2
    (2, 'RK_PA3'): (3, 'RK_PA3'),  # GPIO2_A3 -> GPIO3_A3
    (2, 'RK_PA4'): (3, 'RK_PA4'),  # GPIO2_A4 -> GPIO3_A4
    (2, 'RK_PA5'): (3, 'RK_PA5'),  # GPIO2_A5 -> GPIO3_A5
    (2, 'RK_PA6'): (3, 'RK_PA6'),  # GPIO2_A6 -> GPIO3_A6
    (2, 'RK_PA7'): (3, 'RK_PA7'),  # GPIO2_A7 -> GPIO3_A7
    (2, 'RK_PB0'): (3, 'RK_PB0'),  # GPIO2_B0 -> GPIO3_B0
    (2, 'RK_PB1'): (3, 'RK_PB1'),  # GPIO2_B1 -> GPIO3_B1
    (2, 'RK_PB2'): (3, 'RK_PB2'),  # GPIO2_B2 -> GPIO3_B2
    (2, 'RK_PB3'): (3, 'RK_PB3'),  # GPIO2_B3 -> GPIO3_B3
    (2, 'RK_PB4'): (3, 'RK_PB4'),  # GPIO2_B4 -> GPIO3_B4
    (2, 'RK_PB5'): (3, 'RK_PB5'),  # GPIO2_B5 -> GPIO3_B5
    (2, 'RK_PB6'): (3, 'RK_PB6'),  # GPIO2_B6 -> GPIO3_B6
    (2, 'RK_PB7'): (3, 'RK_PB7'),  # GPIO2_B7 -> GPIO3_B7
    
    # VCCIO7 mappings
    (3, 'RK_PA0'): (4, 'RK_PA0'),  # GPIO3_A0 -> GPIO4_A0
    (3, 'RK_PA1'): (4, 'RK_PA1'),  # GPIO3_A1 -> GPIO4_A1
    (3, 'RK_PA2'): (4, 'RK_PA2'),  # GPIO3_A2 -> GPIO4_A2
    (3, 'RK_PA3'): (4, 'RK_PA3'),  # GPIO3_A3 -> GPIO4_A3
    (3, 'RK_PA4'): (4, 'RK_PA4'),  # GPIO3_A4 -> GPIO4_A4
    (3, 'RK_PA5'): (4, 'RK_PA5'),  # GPIO3_A5 -> GPIO4_A5
    (3, 'RK_PA6'): (4, 'RK_PA6'),  # GPIO3_A6 -> GPIO4_A6
    (3, 'RK_PA7'): (4, 'RK_PA7'),  # GPIO3_A7 -> GPIO4_A7
    (3, 'RK_PB0'): (4, 'RK_PB0'),  # GPIO3_B0 -> GPIO4_B0
    (3, 'RK_PB1'): (4, 'RK_PB1'),  # GPIO3_B1 -> GPIO4_B1
    (3, 'RK_PB2'): (4, 'RK_PB2'),  # GPIO3_B2 -> GPIO4_B2
    (3, 'RK_PB3'): (4, 'RK_PB3'),  # GPIO3_B3 -> GPIO4_B3
    (3, 'RK_PB4'): (4, 'RK_PB4'),  # GPIO3_B4 -> GPIO4_B4
    (3, 'RK_PB5'): (4, 'RK_PB5'),  # GPIO3_B5 -> GPIO4_B5
    (3, 'RK_PB6'): (4, 'RK_PB6'),  # GPIO3_B6 -> GPIO4_B6
    (3, 'RK_PB7'): (4, 'RK_PB7'),  # GPIO3_B7 -> GPIO4_B7
}

def convert_gpio_reference(line):
    """Convert GPIO references in a line according to the mapping"""
    import re
    
    # Pattern to match GPIO references like <gpio_bank RK_Pxy ...>
    pattern = r'<(\d+)\s+(RK_P[A-D]\d+)'
    
    def replace_gpio(match):
        bank = int(match.group(1))
        pin = match.group(2)
        
        if (bank, pin) in PIN_MAPPING:
            new_bank, new_pin = PIN_MAPPING[(bank, pin)]
            return f'<{new_bank} {new_pin}'
        else:
            # Return original if no mapping found
            return match.group(0)
    
    return re.sub(pattern, replace_gpio, line)

def main():
    input_file = 'kernel-6.1/arch/arm64/boot/dts/rockchip/rv1126b-pinctrl.dtsi'
    output_file = 'kernel-6.1/arch/arm64/boot/dts/rockchip/rv1126bp-pinctrl.dtsi'
    
    try:
        with open(input_file, 'r') as f:
            content = f.read()
        
        # Convert GPIO references
        converted_content = convert_gpio_reference(content)
        
        # Update the header comment
        converted_content = converted_content.replace(
            'rv1126b-pinctrl.dtsi',
            'rv1126bp-pinctrl.dtsi'
        )
        
        with open(output_file, 'w') as f:
            f.write(converted_content)
        
        print(f"Conversion completed: {input_file} -> {output_file}")
        
    except FileNotFoundError:
        print(f"Error: Input file {input_file} not found")
    except Exception as e:
        print(f"Error during conversion: {e}")

if __name__ == "__main__":
    main()
