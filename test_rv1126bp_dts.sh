#!/bin/bash

# Test script for RV1126BP device tree files
# This script validates the device tree syntax and checks for common issues

set -e

KERNEL_DIR="kernel-6.1"
DTS_DIR="$KERNEL_DIR/arch/arm64/boot/dts/rockchip"

echo "Testing RV1126BP Device Tree Files..."
echo "======================================"

# Check if device tree compiler is available
if ! command -v dtc &> /dev/null; then
    echo "Error: Device Tree Compiler (dtc) not found. Please install device-tree-compiler package."
    exit 1
fi

# Function to test a device tree file
test_dts_file() {
    local file=$1
    local base_name=$(basename "$file" .dts)
    
    echo "Testing $file..."
    
    # Check if file exists
    if [ ! -f "$file" ]; then
        echo "  ERROR: File not found: $file"
        return 1
    fi
    
    # Check syntax with dtc
    if dtc -I dts -O dtb -o /dev/null "$file" 2>/dev/null; then
        echo "  ✓ Syntax check passed"
    else
        echo "  ✗ Syntax check failed"
        echo "  Detailed error:"
        dtc -I dts -O dtb -o /dev/null "$file" 2>&1 | sed 's/^/    /'
        return 1
    fi
    
    # Check for common issues
    echo "  Checking for common issues..."
    
    # Check for proper includes
    if grep -q '#include.*rv1126bp\.dtsi' "$file"; then
        echo "    ✓ Includes rv1126bp.dtsi"
    else
        echo "    ⚠ Warning: Does not include rv1126bp.dtsi"
    fi
    
    # Check for compatible string
    if grep -q 'compatible.*rv1126bp' "$file"; then
        echo "    ✓ Has rv1126bp compatible string"
    else
        echo "    ⚠ Warning: Missing rv1126bp compatible string"
    fi
    
    # Check for memory node
    if grep -q 'memory@' "$file"; then
        echo "    ✓ Has memory node"
    else
        echo "    ⚠ Warning: Missing memory node"
    fi
    
    # Check for chosen/stdout-path
    if grep -q 'stdout-path' "$file"; then
        echo "    ✓ Has stdout-path in chosen node"
    else
        echo "    ⚠ Warning: Missing stdout-path in chosen node"
    fi
    
    echo "  Test completed for $file"
    echo ""
}

# Test the main device tree files
echo "1. Testing RV1126BP base device tree..."
test_dts_file "$DTS_DIR/rv1126bp.dtsi"

echo "2. Testing RV1126BP pinctrl..."
test_dts_file "$DTS_DIR/rv1126bp-pinctrl.dtsi"

echo "3. Testing RV1126BP EVB device tree..."
test_dts_file "$DTS_DIR/rv1126bp-evb.dts"

echo "4. Testing existing RV1126BP EVB v14..."
if [ -f "$DTS_DIR/rv1126bp-evb-v14.dtsi" ]; then
    test_dts_file "$DTS_DIR/rv1126bp-evb-v14.dtsi"
else
    echo "  File not found: rv1126bp-evb-v14.dtsi (skipping)"
fi

# Check GPIO mapping conversion
echo "5. Checking GPIO mapping conversion..."
echo "Verifying that GPIO references have been properly converted..."

# Check a few sample conversions in the pinctrl file
if [ -f "$DTS_DIR/rv1126bp-pinctrl.dtsi" ]; then
    echo "  Checking sample GPIO conversions in pinctrl file:"
    
    # Look for converted GPIO references
    if grep -q '<1 RK_PA' "$DTS_DIR/rv1126bp-pinctrl.dtsi"; then
        echo "    ✓ Found GPIO1 references (converted from GPIO0)"
    else
        echo "    ⚠ Warning: No GPIO1 references found"
    fi
    
    if grep -q '<2 RK_PA' "$DTS_DIR/rv1126bp-pinctrl.dtsi"; then
        echo "    ✓ Found GPIO2 references (converted from GPIO1)"
    else
        echo "    ⚠ Warning: No GPIO2 references found"
    fi
    
    # Check that old GPIO0 references are minimal (should mostly be converted)
    gpio0_count=$(grep -c '<0 RK_P' "$DTS_DIR/rv1126bp-pinctrl.dtsi" || echo "0")
    echo "    GPIO0 references remaining: $gpio0_count (should be minimal)"
fi

# Check EVB file for proper GPIO usage
if [ -f "$DTS_DIR/rv1126bp-evb.dts" ]; then
    echo "  Checking GPIO usage in EVB file:"
    
    if grep -q 'gpio1 RK_PA' "$DTS_DIR/rv1126bp-evb.dts"; then
        echo "    ✓ Found GPIO1 references in EVB file"
    else
        echo "    ⚠ Warning: No GPIO1 references found in EVB file"
    fi
fi

echo "6. Summary"
echo "=========="
echo "Device tree validation completed."
echo ""
echo "Files created/modified:"
echo "  - $DTS_DIR/rv1126bp.dtsi (main SoC definition)"
echo "  - $DTS_DIR/rv1126bp-pinctrl.dtsi (pin control with GPIO mapping)"
echo "  - $DTS_DIR/rv1126bp-evb.dts (EVB board definition)"
echo "  - $DTS_DIR/Makefile (updated to include new files)"
echo ""
echo "To build the device tree blobs, run:"
echo "  cd $KERNEL_DIR"
echo "  make ARCH=arm64 CROSS_COMPILE=aarch64-linux-gnu- dtbs"
echo ""
echo "Or to build just the RV1126BP files:"
echo "  make ARCH=arm64 CROSS_COMPILE=aarch64-linux-gnu- rv1126bp-evb.dtb"
